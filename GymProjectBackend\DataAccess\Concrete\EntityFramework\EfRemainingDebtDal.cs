using Core.DataAccess.EntityFramework;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfRemainingDebtDal : EfCompanyEntityRepositoryBase<RemainingDebt, GymContext>, IRemainingDebtDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        // Constructor injection (Scalability için)
        public EfRemainingDebtDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext, GymContext context) : base(companyContext, context)
        {
            _companyContext = companyContext;
        }

        public List<RemainingDebtDetailDto> GetRemainingDebtDetails()
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var result = from rd in _context.RemainingDebts
                             join p in _context.Payments on rd.PaymentID equals p.PaymentID
                             join ms in _context.Memberships on p.MemberShipID equals ms.MembershipID
                             join m in _context.Members on ms.MemberID equals m.MemberID
                             where rd.IsActive && rd.RemainingAmount > 0
                             && m.IsActive == true // Member'ın aktif olması kontrolü eklendi
                             && rd.CompanyID == companyId // Şirket ID'sine göre filtrele
                             && p.CompanyID == companyId // Ödemelerin de aynı şirkete ait olduğundan emin ol
                             && ms.CompanyID == companyId // Üyeliklerin de aynı şirkete ait olduğundan emin ol
                             && m.CompanyID == companyId // Üyelerin de aynı şirkete ait olduğundan emin ol
                             select new RemainingDebtDetailDto
                             {
                                 RemainingDebtID = rd.RemainingDebtID,
                                 PaymentID = rd.PaymentID,
                                 MemberName = m.Name,
                                 PhoneNumber = m.PhoneNumber,
                                 OriginalAmount = rd.OriginalAmount,
                                 RemainingAmount = rd.RemainingAmount,
                                 LastUpdateDate = rd.LastUpdateDate,
                                 PaymentMethod = p.PaymentMethod
                             };
                return result.ToList();
            }

            // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        // SOLID prensiplerine uygun: Karmaşık business logic DAL katmanında
        public IResult AddDebtPaymentWithBusinessLogic(DebtPaymentDto debtPaymentDto)
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                using (var scope = new TransactionScope())
                {
                    try
                    {
                        // Mevcut kullanıcının şirket ID'sini al
                        int companyId = _companyContext.GetCompanyId();

                        var remainingDebt = _context.RemainingDebts
                            .FirstOrDefault(rd => rd.RemainingDebtID == debtPaymentDto.RemainingDebtID && rd.CompanyID == companyId);

                        if (remainingDebt == null)
                            return new ErrorResult("Ödeme bulunamadı.");

                        if (debtPaymentDto.PaidAmount > remainingDebt.RemainingAmount)
                            return new ErrorResult("Geçersiz ödeme tutarı.");

                        // Borç ödemesini kaydet
                        var debtPayment = new DebtPayment
                        {
                            RemainingDebtID = debtPaymentDto.RemainingDebtID,
                            CompanyID = companyId,
                            PaidAmount = debtPaymentDto.PaidAmount,
                            PaymentMethod = debtPaymentDto.PaymentMethod,
                            PaymentDate = DateTime.Now,
                            IsActive = true,
                            CreationDate = DateTime.Now
                        };
                        _context.DebtPayments.Add(debtPayment);

                        // Kalan borç tutarını güncelle
                        decimal newRemainingAmount = remainingDebt.RemainingAmount - debtPaymentDto.PaidAmount;
                        remainingDebt.RemainingAmount = newRemainingAmount;
                        remainingDebt.LastUpdateDate = DateTime.Now;
                        _context.RemainingDebts.Update(remainingDebt);

                        // Eğer borç tamamen ödendiyse Payment kaydını güncelle
                        if (newRemainingAmount == 0)
                        {
                            var payment = _context.Payments
                                .FirstOrDefault(p => p.PaymentID == remainingDebt.PaymentID && p.CompanyID == companyId);

                            if (payment != null)
                            {
                                // Null kontrolü yaparak atama yap
                                payment.FinalPaymentMethod = debtPaymentDto.PaymentMethod ?? "Belirsiz";
                                payment.PaymentStatus = "Completed";

                                // Eğer OriginalPaymentMethod null ise, onu da güncelle
                                if (string.IsNullOrEmpty(payment.OriginalPaymentMethod))
                                {
                                    payment.OriginalPaymentMethod = payment.PaymentMethod ?? "Borç";
                                }

                                _context.Payments.Update(payment);
                            }
                        }

                        _context.SaveChanges();
                        scope.Complete();
                        return new SuccessResult("Ödeme başarıyla eklendi.");
                    }
                    catch (Exception ex)
                    {
                        scope.Dispose();
                        return new ErrorResult($"Ödeme işlemi sırasında bir hata oluştu: {ex.Message}");
                    }
                }
            }

            // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Soft delete işlemi DAL katmanında
        /// </summary>
        public IResult SoftDeleteRemainingDebt(int remainingDebtId, int companyId)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    var remainingDebt = _context.RemainingDebts.FirstOrDefault(rd =>
                        rd.RemainingDebtID == remainingDebtId && rd.CompanyID == companyId && rd.IsActive);

                    if (remainingDebt == null)
                    {
                        return new ErrorResult("Borç kaydı bulunamadı veya erişim yetkiniz yok.");
                    }

                    // Soft delete işlemi
                    remainingDebt.IsActive = false;
                    remainingDebt.LastUpdateDate = DateTime.Now;
                    _context.RemainingDebts.Update(remainingDebt);
                    _context.SaveChanges();

                    return new SuccessResult("Borç kaydı başarıyla silindi.");
                }
                else
                {
                    return new ErrorResult("Veritabanı bağlantısı bulunamadı.");
                }
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Borç kaydı silinirken hata oluştu: {ex.Message}");
            }
        }
    }
}
