-- Standart Alanlar Migration Script
-- Bu script tüm tablolara eksik olan CreationDate, UpdatedDate, DeletedDate, IsActive alanlarını ekler
-- Tüm alanlar nullable olarak eklenir ve mevcut yapıya uygun şekilde ayarlanır

USE [GymProject]
GO

PRINT 'Standart alanlar migration başlatılıyor...'
PRINT '========================================'

-- 1. Cities tablosuna eksik alanları ekle
PRINT 'Cities tablosu güncelleniyor...'
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[Cities]') AND name = 'IsActive')
BEGIN
    ALTER TABLE [dbo].[Cities] ADD [IsActive] [bit] NULL DEFAULT(1)
    PRINT '- Cities.IsActive alanı eklendi'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[Cities]') AND name = 'CreationDate')
BEGIN
    ALTER TABLE [dbo].[Cities] ADD [CreationDate] [datetime2](7) NULL DEFAULT(GETDATE())
    PRINT '- Cities.CreationDate alanı eklendi'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[Cities]') AND name = 'UpdatedDate')
BEGIN
    ALTER TABLE [dbo].[Cities] ADD [UpdatedDate] [datetime2](7) NULL
    PRINT '- Cities.UpdatedDate alanı eklendi'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[Cities]') AND name = 'DeletedDate')
BEGIN
    ALTER TABLE [dbo].[Cities] ADD [DeletedDate] [datetime2](7) NULL
    PRINT '- Cities.DeletedDate alanı eklendi'
END

-- 2. Towns tablosuna eksik alanları ekle
PRINT 'Towns tablosu güncelleniyor...'
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[Towns]') AND name = 'IsActive')
BEGIN
    ALTER TABLE [dbo].[Towns] ADD [IsActive] [bit] NULL DEFAULT(1)
    PRINT '- Towns.IsActive alanı eklendi'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[Towns]') AND name = 'CreationDate')
BEGIN
    ALTER TABLE [dbo].[Towns] ADD [CreationDate] [datetime2](7) NULL DEFAULT(GETDATE())
    PRINT '- Towns.CreationDate alanı eklendi'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[Towns]') AND name = 'UpdatedDate')
BEGIN
    ALTER TABLE [dbo].[Towns] ADD [UpdatedDate] [datetime2](7) NULL
    PRINT '- Towns.UpdatedDate alanı eklendi'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[Towns]') AND name = 'DeletedDate')
BEGIN
    ALTER TABLE [dbo].[Towns] ADD [DeletedDate] [datetime2](7) NULL
    PRINT '- Towns.DeletedDate alanı eklendi'
END

-- 3. DebtPayments tablosuna eksik alanları ekle
PRINT 'DebtPayments tablosu güncelleniyor...'
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[DebtPayments]') AND name = 'UpdatedDate')
BEGIN
    ALTER TABLE [dbo].[DebtPayments] ADD [UpdatedDate] [datetime2](7) NULL
    PRINT '- DebtPayments.UpdatedDate alanı eklendi'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[DebtPayments]') AND name = 'DeletedDate')
BEGIN
    ALTER TABLE [dbo].[DebtPayments] ADD [DeletedDate] [datetime2](7) NULL
    PRINT '- DebtPayments.DeletedDate alanı eklendi'
END

-- 4. RemainingDebts tablosuna eksik alanları ekle
PRINT 'RemainingDebts tablosu güncelleniyor...'
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[RemainingDebts]') AND name = 'UpdatedDate')
BEGIN
    ALTER TABLE [dbo].[RemainingDebts] ADD [UpdatedDate] [datetime2](7) NULL
    PRINT '- RemainingDebts.UpdatedDate alanı eklendi'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[RemainingDebts]') AND name = 'DeletedDate')
BEGIN
    ALTER TABLE [dbo].[RemainingDebts] ADD [DeletedDate] [datetime2](7) NULL
    PRINT '- RemainingDebts.DeletedDate alanı eklendi'
END

-- 5. UserDevices tablosunu güncelle (CreatedAt -> CreationDate)
PRINT 'UserDevices tablosu güncelleniyor...'
-- Önce yeni alanları ekle
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[UserDevices]') AND name = 'CreationDate')
BEGIN
    ALTER TABLE [dbo].[UserDevices] ADD [CreationDate] [datetime2](7) NULL
    PRINT '- UserDevices.CreationDate alanı eklendi'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[UserDevices]') AND name = 'UpdatedDate')
BEGIN
    ALTER TABLE [dbo].[UserDevices] ADD [UpdatedDate] [datetime2](7) NULL
    PRINT '- UserDevices.UpdatedDate alanı eklendi'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[UserDevices]') AND name = 'DeletedDate')
BEGIN
    ALTER TABLE [dbo].[UserDevices] ADD [DeletedDate] [datetime2](7) NULL
    PRINT '- UserDevices.DeletedDate alanı eklendi'
END

-- CreatedAt verilerini CreationDate'e kopyala
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[UserDevices]') AND name = 'CreatedAt')
   AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[UserDevices]') AND name = 'CreationDate')
BEGIN
    UPDATE [dbo].[UserDevices] SET [CreationDate] = [CreatedAt] WHERE [CreationDate] IS NULL
    PRINT '- UserDevices: CreatedAt verileri CreationDate alanına kopyalandı'
END

-- 6. MembershipFreezeHistory tablosuna eksik alanları ekle
PRINT 'MembershipFreezeHistory tablosu güncelleniyor...'
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[MembershipFreezeHistory]') AND name = 'IsActive')
BEGIN
    ALTER TABLE [dbo].[MembershipFreezeHistory] ADD [IsActive] [bit] NULL DEFAULT(1)
    PRINT '- MembershipFreezeHistory.IsActive alanı eklendi'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[MembershipFreezeHistory]') AND name = 'UpdatedDate')
BEGIN
    ALTER TABLE [dbo].[MembershipFreezeHistory] ADD [UpdatedDate] [datetime2](7) NULL
    PRINT '- MembershipFreezeHistory.UpdatedDate alanı eklendi'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[MembershipFreezeHistory]') AND name = 'DeletedDate')
BEGIN
    ALTER TABLE [dbo].[MembershipFreezeHistory] ADD [DeletedDate] [datetime2](7) NULL
    PRINT '- MembershipFreezeHistory.DeletedDate alanı eklendi'
END

-- 7. WorkoutProgramDays tablosuna eksik alanları ekle
PRINT 'WorkoutProgramDays tablosu güncelleniyor...'
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[WorkoutProgramDays]') AND name = 'IsActive')
BEGIN
    ALTER TABLE [dbo].[WorkoutProgramDays] ADD [IsActive] [bit] NULL DEFAULT(1)
    PRINT '- WorkoutProgramDays.IsActive alanı eklendi'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[WorkoutProgramDays]') AND name = 'UpdatedDate')
BEGIN
    ALTER TABLE [dbo].[WorkoutProgramDays] ADD [UpdatedDate] [datetime2](7) NULL
    PRINT '- WorkoutProgramDays.UpdatedDate alanı eklendi'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[WorkoutProgramDays]') AND name = 'DeletedDate')
BEGIN
    ALTER TABLE [dbo].[WorkoutProgramDays] ADD [DeletedDate] [datetime2](7) NULL
    PRINT '- WorkoutProgramDays.DeletedDate alanı eklendi'
END

-- 8. WorkoutProgramExercises tablosuna eksik alanları ekle
PRINT 'WorkoutProgramExercises tablosu güncelleniyor...'
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[WorkoutProgramExercises]') AND name = 'IsActive')
BEGIN
    ALTER TABLE [dbo].[WorkoutProgramExercises] ADD [IsActive] [bit] NULL DEFAULT(1)
    PRINT '- WorkoutProgramExercises.IsActive alanı eklendi'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[WorkoutProgramExercises]') AND name = 'UpdatedDate')
BEGIN
    ALTER TABLE [dbo].[WorkoutProgramExercises] ADD [UpdatedDate] [datetime2](7) NULL
    PRINT '- WorkoutProgramExercises.UpdatedDate alanı eklendi'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[WorkoutProgramExercises]') AND name = 'DeletedDate')
BEGIN
    ALTER TABLE [dbo].[WorkoutProgramExercises] ADD [DeletedDate] [datetime2](7) NULL
    PRINT '- WorkoutProgramExercises.DeletedDate alanı eklendi'
END

-- Mevcut kayıtlar için varsayılan değerleri ayarla
PRINT 'Mevcut kayıtlar için varsayılan değerler ayarlanıyor...'

-- Cities için varsayılan değerler
UPDATE [dbo].[Cities] SET [IsActive] = 1 WHERE [IsActive] IS NULL
UPDATE [dbo].[Cities] SET [CreationDate] = GETDATE() WHERE [CreationDate] IS NULL

-- Towns için varsayılan değerler  
UPDATE [dbo].[Towns] SET [IsActive] = 1 WHERE [IsActive] IS NULL
UPDATE [dbo].[Towns] SET [CreationDate] = GETDATE() WHERE [CreationDate] IS NULL

-- MembershipFreezeHistory için varsayılan değerler
UPDATE [dbo].[MembershipFreezeHistory] SET [IsActive] = 1 WHERE [IsActive] IS NULL

-- WorkoutProgramDays için varsayılan değerler
UPDATE [dbo].[WorkoutProgramDays] SET [IsActive] = 1 WHERE [IsActive] IS NULL

-- WorkoutProgramExercises için varsayılan değerler
UPDATE [dbo].[WorkoutProgramExercises] SET [IsActive] = 1 WHERE [IsActive] IS NULL

PRINT '========================================'
PRINT 'Standart alanlar migration tamamlandı!'
PRINT 'Güncellenen tablolar:'
PRINT '- Cities (4 alan eklendi)'
PRINT '- Towns (4 alan eklendi)'
PRINT '- DebtPayments (2 alan eklendi)'
PRINT '- RemainingDebts (2 alan eklendi)'
PRINT '- UserDevices (2 alan eklendi + CreatedAt->CreationDate)'
PRINT '- MembershipFreezeHistory (3 alan eklendi)'
PRINT '- WorkoutProgramDays (3 alan eklendi)'
PRINT '- WorkoutProgramExercises (3 alan eklendi)'
PRINT 'Toplam: 25 alan eklendi'
PRINT '========================================'
GO
